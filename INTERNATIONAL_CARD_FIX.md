# 🌍 International Card Support Fix

## 🚨 **Issue: International Cards Not Supported**

The error "International cards are not supported" is a **Razorpay account configuration issue**, not a code problem. This needs to be fixed in your Razorpay dashboard settings.

## 🔧 **Solution Steps**

### **Step 1: Enable International Payments in Razorpay Dashboard**

1. **Login to Razorpay Dashboard**
   - Go to [dashboard.razorpay.com](https://dashboard.razorpay.com)
   - Login with your Razorpay account credentials

2. **Navigate to Settings**
   - Click on **"Settings"** in the left sidebar
   - Go to **"Payment Methods"** or **"Configuration"**

3. **Enable International Cards**
   - Look for **"International Payments"** section
   - Enable **"International Cards"** option
   - Enable **"International Wallets"** if needed

4. **Configure Supported Countries**
   - Add countries you want to support
   - Common options: US, UK, Canada, Australia, etc.
   - Or select **"All Countries"** for global support

5. **Save Configuration**
   - Click **"Save"** or **"Update"**
   - Changes may take 5-10 minutes to take effect

### **Step 2: Verify Account Activation Status**

1. **Check Account Status**
   - Ensure your Razorpay account is **fully activated**
   - Complete any pending KYC requirements
   - Verify business details if required

2. **Check Payment Gateway Status**
   - Ensure your payment gateway is **live** (not test mode)
   - Verify all required documents are submitted
   - Check for any pending approvals

### **Step 3: Contact Razorpay Support (If Needed)**

If you can't find the international payment settings:

1. **Razorpay Support Channels**
   - Email: <EMAIL>
   - Phone: +91-80-6190-6200
   - Live Chat: Available in dashboard

2. **Information to Provide**
   - Your Razorpay account ID
   - Business type and requirements
   - Countries you need to support
   - Request to enable international payments

## 🛠️ **Alternative Solutions**

### **Option 1: Add Payment Method Configuration**

Add explicit payment method configuration to handle international cards better:

```typescript
// Enhanced Razorpay configuration
const options = {
  key: RAZORPAY_KEY_ID,
  amount: plan.price * 100,
  currency: 'INR',
  name: 'Wolf CTF Challenge',
  description: `${plan.name} - ${plan.duration}`,
  config: {
    display: {
      blocks: {
        banks: {
          name: 'Pay using ' + plan.name,
          instruments: [
            {
              method: 'card',
              types: ['credit', 'debit'],
              banks: ['HDFC', 'ICICI', 'AXIS', 'SBI', 'VISA', 'MASTERCARD']
            },
            {
              method: 'netbanking'
            },
            {
              method: 'wallet'
            },
            {
              method: 'upi'
            }
          ]
        }
      },
      sequence: ['block.banks'],
      preferences: {
        show_default_blocks: true
      }
    }
  },
  // Add international card support
  allow_rotation: true,
  retry: {
    enabled: true,
    max_count: 3
  }
};
```

### **Option 2: Add Better Error Handling**

Enhance the error message to guide users to alternatives:

```typescript
// Enhanced error handling for international cards
razorpay.on('payment.failed', function(response: any) {
  let errorMessage = "Payment failed. Please try again.";
  let errorTitle = "❌ PAYMENT FAILED";

  if (response.error) {
    const error = response.error;
    
    if (error.description?.includes('international') || 
        error.description?.includes('not supported')) {
      errorTitle = "🌍 INTERNATIONAL CARD ISSUE";
      errorMessage = `
        International cards are currently not enabled on our payment gateway.
        
        Please try:
        • Use a domestic Indian card (if available)
        • Try UPI payment (GPay, PhonePe, Paytm)
        • Use Net Banking
        • Contact support: <EMAIL>
        
        We're working to enable international card support soon.
      `;
    }
  }

  toast({
    variant: "destructive",
    title: errorTitle,
    description: errorMessage,
  });
});
```

## 📋 **Razorpay Dashboard Checklist**

### **Account Settings to Verify:**

- [ ] **Account Status**: Fully activated
- [ ] **KYC Status**: Completed
- [ ] **Business Verification**: Approved
- [ ] **International Payments**: Enabled
- [ ] **Supported Countries**: Configured
- [ ] **Payment Methods**: All enabled
- [ ] **Gateway Status**: Live (not test)

### **Payment Method Settings:**

- [ ] **Credit Cards**: Enabled (Visa, Mastercard)
- [ ] **Debit Cards**: Enabled
- [ ] **International Cards**: Enabled
- [ ] **UPI**: Enabled
- [ ] **Net Banking**: Enabled
- [ ] **Wallets**: Enabled

## 🔍 **Testing After Configuration**

### **Test with Different Cards:**

1. **Domestic Indian Cards**
   - Test with Indian Visa/Mastercard
   - Should work immediately

2. **International Cards**
   - Test with US/UK Visa/Mastercard
   - Should work after enabling international payments

3. **Alternative Methods**
   - Test UPI payments
   - Test Net Banking
   - Test Wallet payments

## ⚡ **Immediate Workaround for Users**

While you enable international cards in Razorpay, users can use these alternatives:

### **✅ Recommended Payment Methods (Always Work)**

1. **UPI Payments** (Most Popular)
   - GPay (Google Pay)
   - PhonePe
   - Paytm
   - BHIM UPI
   - Any UPI app

2. **Net Banking**
   - All major Indian banks supported
   - SBI, HDFC, ICICI, Axis, etc.
   - Secure and reliable

3. **Digital Wallets**
   - Paytm Wallet
   - Mobikwik
   - FreeCharge
   - Amazon Pay

### **🔧 How to Enable International Cards in Razorpay**

#### **Method 1: Through Razorpay Dashboard**

1. **Login to Razorpay Dashboard**
   ```
   URL: https://dashboard.razorpay.com
   ```

2. **Navigate to Payment Methods**
   ```
   Dashboard → Settings → Payment Methods
   OR
   Dashboard → Account & Settings → Payment Configuration
   ```

3. **Enable International Payments**
   - Look for "International Payments" section
   - Toggle ON "Accept International Cards"
   - Select supported countries or "All Countries"
   - Save changes

4. **Verify Settings**
   - Check "Payment Methods" tab
   - Ensure "International Cards" shows as "Enabled"
   - Test with a small international card transaction

#### **Method 2: Contact Razorpay Support**

If you can't find the settings or need assistance:

**Email Support:**
```
Email: <EMAIL>
Subject: Enable International Card Payments
```

**Phone Support:**
```
India: +91-80-6190-6200
International: +91-80-6190-6200
```

**Live Chat:**
- Available in Razorpay Dashboard
- Click "Help" or "Support" icon

**Information to Provide:**
- Account ID: Your Razorpay account identifier
- Business Name: Wolf CTF Challenge
- Request: Enable international card payments
- Countries needed: Specify or request "Global"

### **📋 Account Requirements for International Payments**

1. **Account Status**
   - ✅ Fully activated Razorpay account
   - ✅ Completed KYC verification
   - ✅ Business verification approved
   - ✅ Live payment gateway (not test mode)

2. **Documentation Required**
   - Business registration documents
   - Bank account verification
   - Identity verification (Aadhaar/PAN)
   - Address proof

3. **Compliance Requirements**
   - RBI compliance for international transactions
   - FEMA compliance if applicable
   - Tax registration (GST) if required

### **⏰ Timeline for Activation**

- **Instant**: If option is available in dashboard
- **24-48 hours**: If requires manual approval
- **3-5 business days**: If additional documentation needed
- **1-2 weeks**: For new accounts or complex cases

### **🧪 Testing International Card Support**

Once enabled, test with:

1. **Test International Cards**
   ```
   Card: ************** 1111 (Visa)
   Expiry: Any future date
   CVV: Any 3 digits
   ```

2. **Real International Cards**
   - Start with small amounts
   - Test different card types (Visa, Mastercard)
   - Verify from different countries

### **💡 Alternative Solutions**

If international cards cannot be enabled immediately:

1. **PayPal Integration**
   - Add PayPal as payment option
   - Supports international users
   - Requires separate integration

2. **Cryptocurrency Payments**
   - Accept Bitcoin/Ethereum
   - Global accessibility
   - Requires crypto payment gateway

3. **Bank Transfer Instructions**
   - Provide bank details for wire transfers
   - Manual verification process
   - For high-value transactions

### **📞 Support Information for Users**

Add this to your payment page:

```
🌍 International Card Issues?

International cards are currently being enabled on our payment gateway.

Meanwhile, please use:
• UPI (GPay, PhonePe, Paytm) - Instant & Secure
• Net Banking - All Indian banks supported
• Digital Wallets - Paytm, Mobikwik, etc.

Need help? Contact: <EMAIL>
We'll assist you with payment options!
```

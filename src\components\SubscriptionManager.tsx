import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/firebase';
import { collection, query, orderBy, limit, getDocs, doc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { Search, Calendar, CreditCard, User, AlertCircle, CheckCircle } from 'lucide-react';

interface PaymentRecord {
  id: string;
  userId: string;
  razorpayPaymentId: string;
  amount: number;
  planType: string;
  status: string;
  createdAt: any;
  userEmail?: string;
  userName?: string;
}

interface SubscriptionData {
  userId: string;
  subscriptionType: string;
  subscriptionStatus: string;
  subscriptionExpiryDate: any;
  userEmail?: string;
  userName?: string;
}

export const SubscriptionManager = () => {
  const [payments, setPayments] = useState<PaymentRecord[]>([]);
  const [subscriptions, setSubscriptions] = useState<SubscriptionData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'payments' | 'subscriptions'>('payments');
  const { toast } = useToast();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load recent payments
      const paymentsQuery = query(
        collection(db, 'payments'),
        orderBy('createdAt', 'desc'),
        limit(50)
      );
      const paymentsSnapshot = await getDocs(paymentsQuery);
      const paymentsData = paymentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as PaymentRecord[];

      // Load users with subscriptions
      const usersQuery = query(
        collection(db, 'users'),
        orderBy('updatedAt', 'desc'),
        limit(100)
      );
      const usersSnapshot = await getDocs(usersQuery);
      const subscriptionsData = usersSnapshot.docs
        .map(doc => ({
          userId: doc.id,
          userEmail: doc.data().email,
          userName: doc.data().displayName || doc.data().fullName,
          subscriptionType: doc.data().subscriptionType,
          subscriptionStatus: doc.data().subscriptionStatus,
          subscriptionExpiryDate: doc.data().subscriptionExpiryDate,
        }))
        .filter(user => user.subscriptionType) as SubscriptionData[];

      setPayments(paymentsData);
      setSubscriptions(subscriptionsData);
    } catch (error) {
      console.error('Error loading data:', error);
      toast({
        variant: "destructive",
        title: "❌ LOADING ERROR",
        description: "Failed to load subscription data",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateSubscriptionStatus = async (userId: string, status: 'active' | 'expired' | 'cancelled') => {
    try {
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        subscriptionStatus: status,
        hasAccess: status === 'active',
        updatedAt: serverTimestamp()
      });

      toast({
        title: "✅ STATUS UPDATED",
        description: `Subscription status updated to ${status}`,
      });

      // Reload data
      loadData();
    } catch (error) {
      console.error('Error updating subscription:', error);
      toast({
        variant: "destructive",
        title: "❌ UPDATE ERROR",
        description: "Failed to update subscription status",
      });
    }
  };

  const filteredPayments = payments.filter(payment =>
    payment.userEmail?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    payment.userName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    payment.razorpayPaymentId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredSubscriptions = subscriptions.filter(sub =>
    sub.userEmail?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    sub.userName?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'expired': return 'bg-yellow-500';
      case 'cancelled': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="terminal-cursor text-2xl mb-4">LOADING</div>
        <p className="text-muted-foreground">Loading subscription data...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <h2 className="text-2xl font-bold">💳 SUBSCRIPTION MANAGER</h2>
        <div className="flex gap-2">
          <Button
            variant={activeTab === 'payments' ? 'default' : 'outline'}
            onClick={() => setActiveTab('payments')}
          >
            <CreditCard className="h-4 w-4 mr-2" />
            PAYMENTS ({payments.length})
          </Button>
          <Button
            variant={activeTab === 'subscriptions' ? 'default' : 'outline'}
            onClick={() => setActiveTab('subscriptions')}
          >
            <User className="h-4 w-4 mr-2" />
            SUBSCRIPTIONS ({subscriptions.length})
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search by email, name, or payment ID..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Content */}
      {activeTab === 'payments' ? (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Recent Payments</h3>
          {filteredPayments.length === 0 ? (
            <Card className="p-8 text-center">
              <p className="text-muted-foreground">No payments found</p>
            </Card>
          ) : (
            <div className="grid gap-4">
              {filteredPayments.map((payment) => (
                <Card key={payment.id} className="p-4 border-2">
                  <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CreditCard className="h-4 w-4" />
                        <span className="font-mono text-sm">{payment.razorpayPaymentId}</span>
                        <Badge className={getStatusColor(payment.status)}>
                          {payment.status.toUpperCase()}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <p>User: {payment.userEmail || 'Unknown'}</p>
                        <p>Plan: {payment.planType} - ₹{payment.amount}</p>
                        <p>Date: {formatDate(payment.createdAt)}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold">₹{payment.amount}</div>
                      <Badge variant="outline">{payment.planType}</Badge>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Active Subscriptions</h3>
          {filteredSubscriptions.length === 0 ? (
            <Card className="p-8 text-center">
              <p className="text-muted-foreground">No subscriptions found</p>
            </Card>
          ) : (
            <div className="grid gap-4">
              {filteredSubscriptions.map((sub) => (
                <Card key={sub.userId} className="p-4 border-2">
                  <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span className="font-semibold">{sub.userName || 'Unknown User'}</span>
                        <Badge className={getStatusColor(sub.subscriptionStatus)}>
                          {sub.subscriptionStatus?.toUpperCase() || 'UNKNOWN'}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <p>Email: {sub.userEmail}</p>
                        <p>Plan: {sub.subscriptionType}</p>
                        <p>Expires: {formatDate(sub.subscriptionExpiryDate)}</p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateSubscriptionStatus(sub.userId, 'active')}
                        disabled={sub.subscriptionStatus === 'active'}
                      >
                        <CheckCircle className="h-4 w-4 mr-1" />
                        ACTIVATE
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateSubscriptionStatus(sub.userId, 'cancelled')}
                        disabled={sub.subscriptionStatus === 'cancelled'}
                      >
                        <AlertCircle className="h-4 w-4 mr-1" />
                        CANCEL
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Refresh Button */}
      <div className="text-center">
        <Button onClick={loadData} variant="outline">
          🔄 REFRESH DATA
        </Button>
      </div>
    </div>
  );
};

export default SubscriptionManager;

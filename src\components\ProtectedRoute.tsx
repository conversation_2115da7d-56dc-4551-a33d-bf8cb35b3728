import { useEffect, useState } from 'react';
import { useAuth } from '@/components/AuthProvider';
import { useNavigate } from 'react-router-dom';
import { checkUserSubscription } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireSubscription?: boolean;
}

export const ProtectedRoute = ({ children, requireSubscription = false }: ProtectedRouteProps) => {
  const { currentUser, loading } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [subscriptionChecked, setSubscriptionChecked] = useState(false);
  const [hasAccess, setHasAccess] = useState(false);

  useEffect(() => {
    const checkAccess = async () => {
      // If not logged in, redirect to login
      if (!loading && !currentUser) {
        navigate('/', { replace: true });
        return;
      }

      // If logged in but subscription check is required
      if (currentUser && requireSubscription) {
        try {
          const subscription = await checkUserSubscription(currentUser.uid);
          
          if (subscription.hasAccess && subscription.status === 'active') {
            setHasAccess(true);
          } else {
            toast({
              title: "🔒 SUBSCRIPTION REQUIRED",
              description: "Please purchase a subscription to access CTF challenges",
            });
            navigate('/payment', { replace: true });
            return;
          }
        } catch (error) {
          console.error('Error checking subscription:', error);
          toast({
            variant: "destructive",
            title: "❌ ACCESS CHECK FAILED",
            description: "Unable to verify subscription. Please try again.",
          });
          navigate('/payment', { replace: true });
          return;
        }
      } else if (currentUser && !requireSubscription) {
        setHasAccess(true);
      }

      setSubscriptionChecked(true);
    };

    checkAccess();
  }, [currentUser, loading, requireSubscription, navigate, toast]);

  // Show loading while checking authentication and subscription
  if (loading || !subscriptionChecked) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="terminal-cursor text-4xl mb-4">VERIFYING ACCESS</div>
          <p className="text-muted-foreground font-mono">
            {loading ? 'Checking authentication...' : 'Verifying subscription...'}
          </p>
        </div>
      </div>
    );
  }

  // If user doesn't have access, don't render children (will redirect)
  if (!hasAccess) {
    return null;
  }

  // Render protected content
  return <>{children}</>;
};

export default ProtectedRoute;

import { useEffect, useState } from 'react';
import { useAuth } from '@/components/AuthProvider';
import { useNavigate, useLocation } from 'react-router-dom';
import { checkUserSubscription } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireSubscription?: boolean;
}

export const ProtectedRoute = ({ children, requireSubscription = false }: ProtectedRouteProps) => {
  const { currentUser, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const [subscriptionChecked, setSubscriptionChecked] = useState(false);
  const [hasAccess, setHasAccess] = useState(false);
  const [checkingSubscription, setCheckingSubscription] = useState(false);

  // Check if we're coming from a payment success (to avoid immediate redirect back)
  const isFromPayment = location.state?.fromPayment || false;

  useEffect(() => {
    const checkAccess = async () => {
      // If not logged in, redirect to login
      if (!loading && !currentUser) {
        navigate('/', { replace: true });
        return;
      }

      // If logged in but subscription check is required
      if (currentUser && requireSubscription) {
        setCheckingSubscription(true);
        console.log('ProtectedRoute: Checking subscription for user:', currentUser.uid);

        try {
          // Add retry logic for subscription check to handle timing issues
          let subscriptionValid = false;
          let attempts = 0;
          const maxAttempts = isFromPayment ? 5 : 3; // More attempts if coming from payment

          while (!subscriptionValid && attempts < maxAttempts) {
            attempts++;
            console.log(`ProtectedRoute: Subscription check attempt ${attempts}/${maxAttempts} (fromPayment: ${isFromPayment})`);

            const subscription = await checkUserSubscription(currentUser.uid);
            console.log('ProtectedRoute: Subscription status:', subscription);

            if (subscription.hasAccess && subscription.status === 'active') {
              console.log('ProtectedRoute: Access granted');
              subscriptionValid = true;
              setHasAccess(true);
            } else if (attempts < maxAttempts) {
              // Wait before retry, longer delay if coming from payment
              const delay = isFromPayment ? 2000 : 1000;
              console.log(`ProtectedRoute: Access denied, retrying in ${delay}ms...`);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }

          if (!subscriptionValid) {
            console.log('ProtectedRoute: Access denied after all attempts');
            toast({
              title: "🔒 SUBSCRIPTION REQUIRED",
              description: "Please purchase a subscription to access CTF challenges",
            });
            navigate('/payment', { replace: true });
            return;
          }
        } catch (error) {
          console.error('ProtectedRoute: Error checking subscription:', error);
          toast({
            variant: "destructive",
            title: "❌ ACCESS CHECK FAILED",
            description: "Unable to verify subscription. Please try again.",
          });
          navigate('/payment', { replace: true });
          return;
        } finally {
          setCheckingSubscription(false);
        }
      } else if (currentUser && !requireSubscription) {
        setHasAccess(true);
      }

      setSubscriptionChecked(true);
    };

    checkAccess();
  }, [currentUser, loading, requireSubscription, navigate, toast]);

  // Show loading while checking authentication and subscription
  if (loading || !subscriptionChecked || checkingSubscription) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="terminal-cursor text-4xl mb-4">VERIFYING ACCESS</div>
          <p className="text-muted-foreground font-mono">
            {loading
              ? 'Checking authentication...'
              : checkingSubscription
                ? 'Verifying subscription access...'
                : 'Finalizing access verification...'}
          </p>
        </div>
      </div>
    );
  }

  // If user doesn't have access, don't render children (will redirect)
  if (!hasAccess) {
    return null;
  }

  // Render protected content
  return <>{children}</>;
};

export default ProtectedRoute;

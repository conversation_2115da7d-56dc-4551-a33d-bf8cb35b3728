# 🔐 Firestore Rules Deployment Script (PowerShell)
# Wolf CTF Challenge Platform

Write-Host "🚀 Deploying Firestore Security Rules..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Check if Firebase CLI is installed
try {
    firebase --version | Out-Null
    Write-Host "✅ Firebase CLI found!" -ForegroundColor Green
} catch {
    Write-Host "❌ Firebase CLI not found!" -ForegroundColor Red
    Write-Host "📦 Installing Firebase CLI..." -ForegroundColor Yellow
    npm install -g firebase-tools
}

# Check if user is logged in
Write-Host "🔐 Checking Firebase authentication..." -ForegroundColor Yellow
try {
    firebase projects:list | Out-Null
    Write-Host "✅ Firebase authentication verified!" -ForegroundColor Green
} catch {
    Write-Host "🔑 Please login to Firebase..." -ForegroundColor Yellow
    firebase login
}

# Show current project
Write-Host "📋 Current Firebase project:" -ForegroundColor Cyan
firebase use

# Validate rules syntax
Write-Host "✅ Validating Firestore rules syntax..." -ForegroundColor Yellow
try {
    firebase firestore:rules validate
    Write-Host "✅ Rules syntax is valid!" -ForegroundColor Green
} catch {
    Write-Host "❌ Rules syntax validation failed!" -ForegroundColor Red
    Write-Host "🔧 Please check firestore.rules file for syntax errors" -ForegroundColor Yellow
    exit 1
}

# Deploy rules
Write-Host "🚀 Deploying Firestore rules..." -ForegroundColor Yellow
try {
    firebase deploy --only firestore:rules
    Write-Host "✅ Firestore rules deployed successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to deploy Firestore rules!" -ForegroundColor Red
    exit 1
}

# Verify deployment
Write-Host "🔍 Verifying deployed rules..." -ForegroundColor Yellow
firebase firestore:rules get

Write-Host ""
Write-Host "🎉 Firestore Rules Deployment Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "✅ Security rules are now active" -ForegroundColor Green
Write-Host "✅ All collections are protected" -ForegroundColor Green
Write-Host "✅ Data validation is enforced" -ForegroundColor Green
Write-Host "✅ Anti-cheating measures active" -ForegroundColor Green
Write-Host ""
Write-Host "🔗 Monitor your rules at:" -ForegroundColor Cyan
Write-Host "   https://console.firebase.google.com/project/cyber-wolf-community-ctf/firestore/rules" -ForegroundColor Blue
Write-Host ""
Write-Host "🧪 Test your rules using the Firebase Console Rules Playground" -ForegroundColor Yellow
Write-Host "📊 Monitor rule usage in Firebase Console Analytics" -ForegroundColor Yellow

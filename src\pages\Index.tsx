import { useAuth } from '@/components/AuthProvider';
import { AuthCard } from '@/components/AuthCard';
import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';

const Index = () => {
  const { currentUser, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    // Security: Block right-click context menu
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      return false;
    };

    // Security: Block common developer shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      // Block F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
      if (
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
        (e.ctrlKey && e.key === 'U')
      ) {
        e.preventDefault();
        return false;
      }
    };

    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  useEffect(() => {
    if (currentUser && !loading) {
      navigate('/payment');
    }
  }, [currentUser, loading, navigate]);

  const handleAuthSuccess = () => {
    navigate('/payment');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="terminal-cursor text-4xl mb-4">INITIALIZING</div>
          <p className="text-muted-foreground font-mono">Loading Wolf authentication system...</p>
        </div>
      </div>
    );
  }

  if (currentUser) {
    return null; // Will redirect to /ctf
  }

  return <AuthCard onAuthSuccess={handleAuthSuccess} />;
};

export default Index;
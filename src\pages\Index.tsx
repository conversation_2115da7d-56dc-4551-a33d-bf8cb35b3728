import { useAuth } from '@/components/AuthProvider';
import { AuthCard } from '@/components/AuthCard';
import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { checkUserSubscription } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';

const Index = () => {
  const { currentUser, loading } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [checkingSubscription, setCheckingSubscription] = useState(false);

  useEffect(() => {
    // Security: Block right-click context menu
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      return false;
    };

    // Security: Block common developer shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      // Block F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
      if (
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J')) ||
        (e.ctrlKey && e.key === 'U')
      ) {
        e.preventDefault();
        return false;
      }
    };

    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  useEffect(() => {
    const handleUserRedirect = async () => {
      if (currentUser && !loading) {
        setCheckingSubscription(true);

        try {
          console.log('Index: Checking subscription for user:', currentUser.uid);
          const subscription = await checkUserSubscription(currentUser.uid);
          console.log('Index: Subscription status:', subscription);

          if (subscription.hasAccess && subscription.status === 'active') {
            // User has active subscription, go directly to CTF
            toast({
              title: "🎯 WELCOME BACK",
              description: `Your ${subscription.subscriptionType} subscription is active!`,
            });
            navigate('/ctf');
          } else {
            // User needs to purchase subscription
            navigate('/payment');
          }
        } catch (error) {
          console.error('Index: Error checking subscription:', error);
          // If subscription check fails, go to payment page to be safe
          navigate('/payment');
        } finally {
          setCheckingSubscription(false);
        }
      }
    };

    handleUserRedirect();
  }, [currentUser, loading, navigate, toast]);

  const handleAuthSuccess = async (isNewUser: boolean = false) => {
    if (isNewUser) {
      // New users always go to payment first
      toast({
        title: "🎉 WELCOME TO WOLF CTF",
        description: "Please select a subscription plan to access challenges!",
      });
      navigate('/payment');
    } else {
      // For existing users, we'll check subscription in the useEffect above
      // This will be triggered by the currentUser change
    }
  };

  if (loading || checkingSubscription) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="terminal-cursor text-4xl mb-4">
            {loading ? 'INITIALIZING' : 'CHECKING ACCESS'}
          </div>
          <p className="text-muted-foreground font-mono">
            {loading
              ? 'Loading Wolf authentication system...'
              : 'Verifying subscription status...'}
          </p>
        </div>
      </div>
    );
  }

  if (currentUser) {
    return null; // Will redirect to /ctf
  }

  return <AuthCard onAuthSuccess={handleAuthSuccess} />;
};

export default Index;
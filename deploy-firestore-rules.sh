#!/bin/bash

# 🔐 Firestore Rules Deployment Script
# Wolf CTF Challenge Platform

echo "🚀 Deploying Firestore Security Rules..."
echo "========================================"

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI not found!"
    echo "📦 Installing Firebase CLI..."
    npm install -g firebase-tools
fi

# Check if user is logged in
echo "🔐 Checking Firebase authentication..."
if ! firebase projects:list &> /dev/null; then
    echo "🔑 Please login to Firebase..."
    firebase login
fi

# Show current project
echo "📋 Current Firebase project:"
firebase use

# Validate rules syntax
echo "✅ Validating Firestore rules syntax..."
if firebase firestore:rules validate; then
    echo "✅ Rules syntax is valid!"
else
    echo "❌ Rules syntax validation failed!"
    echo "🔧 Please check firestore.rules file for syntax errors"
    exit 1
fi

# Deploy rules
echo "🚀 Deploying Firestore rules..."
if firebase deploy --only firestore:rules; then
    echo "✅ Firestore rules deployed successfully!"
else
    echo "❌ Failed to deploy Firestore rules!"
    exit 1
fi

# Verify deployment
echo "🔍 Verifying deployed rules..."
firebase firestore:rules get

echo ""
echo "🎉 Firestore Rules Deployment Complete!"
echo "========================================"
echo "✅ Security rules are now active"
echo "✅ All collections are protected"
echo "✅ Data validation is enforced"
echo "✅ Anti-cheating measures active"
echo ""
echo "🔗 Monitor your rules at:"
echo "   https://console.firebase.google.com/project/cyber-wolf-community-ctf/firestore/rules"
echo ""
echo "🧪 Test your rules using the Firebase Console Rules Playground"
echo "📊 Monitor rule usage in Firebase Console Analytics"

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // ========================================
    // USERS COLLECTION - User Profile Management
    // ========================================
    match /users/{userId} {
      // Allow all authenticated users to read user profiles (for leaderboard)
      allow read: if request.auth != null;

      // Allow users to create their own profile with validation
      allow create: if request.auth != null
        && request.auth.uid == userId
        && validateUserProfile(request.resource.data)
        && request.resource.data.uid == userId
        && request.resource.data.email == request.auth.token.email;

      // Allow users to update their own profile with validation
      allow update: if request.auth != null
        && request.auth.uid == userId
        && validateUserProfileUpdate(request.resource.data, resource.data);

      // Allow admins full access to all user profiles
      allow read, write: if request.auth != null && isAdmin();
    }

    // ========================================
    // SUBMISSIONS COLLECTION - Flag Submission Logs
    // ========================================
    match /submissions/{submissionId} {
      // Users can only read their own submissions
      allow read: if request.auth != null
        && resource.data.userId == request.auth.uid;

      // Users can create submissions with validation
      allow create: if request.auth != null
        && request.auth.uid == request.resource.data.userId
        && validateSubmission(request.resource.data);

      // Submissions are immutable (cannot be updated or deleted)
      allow update, delete: if false;

      // Admins can read all submissions for monitoring
      allow read: if request.auth != null && isAdmin();
    }

    // ========================================
    // PAYMENTS COLLECTION - Payment Records
    // ========================================
    match /payments/{paymentId} {
      // Users can read their own payment records, admins can read all
      allow read: if request.auth != null &&
        (resource.data.userId == request.auth.uid || isAdmin());

      // Users can create payment records with validation
      allow create: if request.auth != null
        && request.auth.uid == request.resource.data.userId
        && validatePayment(request.resource.data);

      // Payment records are immutable after creation
      allow update, delete: if false;

      // Admins can manage all payment records
      allow write: if request.auth != null && isAdmin();
    }

    // ========================================
    // SUBSCRIPTIONS COLLECTION - User Subscriptions
    // ========================================
    match /subscriptions/{subscriptionId} {
      // Users can read their own subscription, admins can read all
      allow read: if request.auth != null &&
        (resource.data.userId == request.auth.uid || isAdmin());

      // Only system/admin can create subscriptions (after payment)
      allow create: if request.auth != null && isAdmin();

      // Only system/admin can update subscriptions
      allow update: if request.auth != null && isAdmin();

      // Subscriptions cannot be deleted
      allow delete: if false;
    }

    // ========================================
    // ADMIN COLLECTION - Administrative Data
    // ========================================
    match /admin/{document=**} {
      allow read, write: if request.auth != null && isAdmin();
    }

    // ========================================
    // CTF PROBLEMS COLLECTION - Challenge Data (if stored in Firestore)
    // ========================================
    match /ctf_problems/{problemId} {
      // All authenticated users can read problems
      allow read: if request.auth != null;

      // Only admins can manage problems
      allow write: if request.auth != null && isAdmin();
    }

    // ========================================
    // LEADERBOARD COLLECTION - Cached Leaderboard Data
    // ========================================
    match /leaderboard/{document} {
      // All authenticated users can read leaderboard
      allow read: if request.auth != null;

      // Only system/admin can update leaderboard
      allow write: if request.auth != null && isAdmin();
    }

    // ========================================
    // SECURITY LOGS COLLECTION - Security Event Logging
    // ========================================
    match /security_logs/{logId} {
      // Only admins can read security logs
      allow read: if request.auth != null && isAdmin();

      // System can create security logs
      allow create: if request.auth != null;

      // Security logs are immutable
      allow update, delete: if false;
    }

    // ========================================
    // DEFAULT DENY RULE - Secure by Default
    // ========================================
    match /{document=**} {
      allow read, write: if false;
    }
  }

  // ========================================
  // HELPER FUNCTIONS
  // ========================================

  // Check if user is admin
  function isAdmin() {
    return request.auth != null &&
           request.auth.token.email == '<EMAIL>';
  }

  // ========================================
  // VALIDATION FUNCTIONS
  // ========================================

  // Validate user profile data structure
  function validateUserProfile(data) {
    return data.keys().hasAll(['uid', 'email', 'displayName', 'fullName', 'score', 'solvedProblems', 'createdAt', 'profileComplete', 'isActive'])
      && data.uid is string
      && data.email is string
      && data.displayName is string
      && data.fullName is string
      && data.score is number
      && data.score >= 0
      && data.score <= 1000
      && data.solvedProblems is list
      && data.profileComplete is bool
      && data.isActive is bool
      && data.createdAt is timestamp;
  }

  // Validate user profile updates (more restrictive but allow subscription fields)
  function validateUserProfileUpdate(newData, oldData) {
    // Allow subscription updates by checking if subscription fields are being added/updated
    let isSubscriptionUpdate = newData.keys().hasAny(['subscriptionType', 'subscriptionStatus', 'subscriptionStartDate', 'subscriptionExpiryDate', 'hasAccess', 'lastPaymentDate']);

    if (isSubscriptionUpdate) {
      // For subscription updates, validate subscription fields
      return (newData.subscriptionType == 'weekly' || newData.subscriptionType == 'monthly')
        && (newData.subscriptionStatus == 'active' || newData.subscriptionStatus == 'expired' || newData.subscriptionStatus == 'cancelled')
        && newData.subscriptionStartDate is timestamp
        && newData.subscriptionExpiryDate is timestamp
        && newData.hasAccess is bool
        && newData.updatedAt is timestamp;
    } else {
      // For regular profile updates, use strict validation
      return newData.keys().hasAll(['uid', 'email', 'displayName', 'fullName', 'score', 'solvedProblems', 'updatedAt'])
        // Core fields cannot be changed
        && newData.uid == oldData.uid
        && newData.email == oldData.email
        // Score can only increase or stay same (prevent cheating)
        && newData.score >= oldData.score
        && newData.score <= 1000
        // Solved problems can only grow (cannot remove solved problems)
        && newData.solvedProblems.size() >= oldData.solvedProblems.size()
        // Must have updatedAt timestamp
        && newData.updatedAt is timestamp
        // Display name and full name can be updated
        && newData.displayName is string
        && newData.fullName is string;
    }
  }

  // Validate flag submission data
  function validateSubmission(data) {
    return data.keys().hasAll(['userId', 'problemId', 'submittedFlag', 'isCorrect', 'timestamp'])
      && data.userId is string
      && data.problemId is string
      && data.submittedFlag is string
      && data.isCorrect is bool
      && data.timestamp is timestamp
      // Optional fields validation
      && (!data.keys().hasAny(['userAgent']) || data.userAgent is string)
      && (!data.keys().hasAny(['ip']) || data.ip is string);
  }

  // Validate payment record data
  function validatePayment(data) {
    return data.keys().hasAll(['userId', 'amount', 'currency', 'planType', 'timestamp'])
      && data.userId is string
      && data.amount is number
      && data.amount > 0
      && data.currency is string
      && data.currency == 'INR'
      && data.planType is string
      && (data.planType == 'weekly' || data.planType == 'monthly')
      && data.timestamp is timestamp
      // Razorpay specific fields
      && (!data.keys().hasAny(['razorpayPaymentId']) || data.razorpayPaymentId is string)
      && (!data.keys().hasAny(['razorpayOrderId']) || data.razorpayOrderId is string)
      && (!data.keys().hasAny(['razorpaySignature']) || data.razorpaySignature is string);
  }

  // Validate subscription data
  function validateSubscription(data) {
    return data.keys().hasAll(['userId', 'subscriptionType', 'status', 'startDate', 'expiryDate'])
      && data.userId is string
      && data.subscriptionType is string
      && (data.subscriptionType == 'weekly' || data.subscriptionType == 'monthly')
      && data.status is string
      && (data.status == 'active' || data.status == 'expired' || data.status == 'cancelled')
      && data.startDate is timestamp
      && data.expiryDate is timestamp
      && data.expiryDate > data.startDate;
  }
}


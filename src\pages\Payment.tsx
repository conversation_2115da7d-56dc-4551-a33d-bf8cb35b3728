import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/components/AuthProvider';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { createPaymentRecord, updateUserSubscription, checkUserSubscription, getUserProfile } from '@/lib/firebase';
import { PaymentSecurity } from '@/lib/paymentSecurity';
import PaymentTroubleshooting from '@/components/PaymentTroubleshooting';
import { Shield, Lock, CreditCard, Clock, CheckCircle, AlertTriangle, HelpCircle } from 'lucide-react';

declare global {
  interface Window {
    Razorpay: any;
  }
}

interface PaymentPlan {
  id: string;
  name: string;
  price: number;
  duration: string;
  features: string[];
  popular?: boolean;
}

const Payment = () => {
  const { currentUser, loading } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [userProfile, setUserProfile] = useState<any>(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState<any>(null);
  const [processingPayment, setProcessingPayment] = useState<string | null>(null);
  const [loadingProfile, setLoadingProfile] = useState(true);
  const [showTroubleshooting, setShowTroubleshooting] = useState(false);

  // Razorpay credentials - LIVE MODE (Real payments will be processed)
  const RAZORPAY_KEY_ID = 'rzp_live_ds31O8lmHsfWi4';

  const paymentPlans: PaymentPlan[] = [
    {
      id: 'weekly',
      name: 'Weekly Access',
      price: 9,
      duration: '7 days',
      features: [
        'Full CTF Challenge Access',
        'Real-time Leaderboard',
        'Flag Submission System',
        'Progress Tracking',
        '24/7 Platform Access'
      ]
    },
    {
      id: 'monthly',
      name: 'Monthly Access',
      price: 29,
      duration: '30 days',
      features: [
        'Full CTF Challenge Access',
        'Real-time Leaderboard',
        'Flag Submission System',
        'Progress Tracking',
        '24/7 Platform Access',
        'Priority Support',
        'Advanced Analytics'
      ],
      popular: true
    }
  ];

  useEffect(() => {
    if (!loading && !currentUser) {
      navigate('/', { replace: true });
      return;
    }

    if (currentUser) {
      loadUserData();
    }
  }, [currentUser, loading, navigate]);

  const loadUserData = async () => {
    if (!currentUser) return;

    try {
      setLoadingProfile(true);
      
      // Load user profile
      const profile = await getUserProfile(currentUser.uid);
      setUserProfile(profile);

      // Check subscription status
      const subscription = await checkUserSubscription(currentUser.uid);
      setSubscriptionStatus(subscription);

      // If user already has active subscription, redirect to CTF
      if (subscription.hasAccess && subscription.status === 'active') {
        toast({
          title: "🎯 ACCESS GRANTED",
          description: `You have active ${subscription.subscriptionType} subscription`,
        });
        navigate('/ctf');
        return;
      }

    } catch (error) {
      console.error('Error loading user data:', error);
      toast({
        variant: "destructive",
        title: "❌ LOADING ERROR",
        description: "Failed to load user data. Please refresh the page.",
      });
    } finally {
      setLoadingProfile(false);
    }
  };

  const loadRazorpayScript = () => {
    return new Promise((resolve) => {
      // Check if Razorpay is already loaded
      if (window.Razorpay) {
        resolve(true);
        return;
      }

      // Check if script is already being loaded
      const existingScript = document.querySelector('script[src*="razorpay"]');
      if (existingScript) {
        existingScript.addEventListener('load', () => resolve(true));
        existingScript.addEventListener('error', () => resolve(false));
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => resolve(true);
      script.onerror = () => resolve(false);
      document.body.appendChild(script);
    });
  };

  const handlePayment = async (plan: PaymentPlan) => {
    if (!currentUser || !userProfile) {
      toast({
        variant: "destructive",
        title: "❌ AUTHENTICATION ERROR",
        description: "Please login to continue with payment",
      });
      return;
    }

    // Check rate limiting
    const rateLimitAllowed = PaymentSecurity.checkRateLimit(currentUser.uid, 3, 15);
    if (!rateLimitAllowed) {
      toast({
        variant: "destructive",
        title: "⏰ TOO MANY ATTEMPTS",
        description: "Too many payment attempts. Please wait 15 minutes before trying again.",
      });
      return;
    }

    setProcessingPayment(plan.id);

    try {
      // Load Razorpay script
      const scriptLoaded = await loadRazorpayScript();
      if (!scriptLoaded) {
        throw new Error('Failed to load Razorpay SDK');
      }

      // Create order options with enhanced configuration
      const options = {
        key: RAZORPAY_KEY_ID,
        amount: plan.price * 100, // Amount in paise
        currency: 'INR',
        name: 'Wolf CTF Challenge',
        description: `${plan.name} - ${plan.duration}`,
        handler: async (response: any) => {
          await handlePaymentSuccess(response, plan);
        },
        prefill: {
          name: PaymentSecurity.sanitizeUserInput(userProfile.displayName || userProfile.fullName || 'CTF Participant'),
          email: PaymentSecurity.sanitizeUserInput(currentUser.email || ''),
          contact: ''
        },
        notes: {
          ...PaymentSecurity.generatePaymentNotes(currentUser.uid, plan.id),
          sessionToken: PaymentSecurity.generateSessionToken(currentUser.uid, plan.id)
        },
        theme: {
          color: '#000000'
        },
        config: {
          display: {
            blocks: {
              recommended: {
                name: 'Recommended Payment Methods',
                instruments: [
                  {
                    method: 'upi'
                  },
                  {
                    method: 'netbanking'
                  },
                  {
                    method: 'wallet'
                  }
                ]
              },
              cards: {
                name: 'Cards (Domestic Only)',
                instruments: [
                  {
                    method: 'card',
                    types: ['credit', 'debit']
                  }
                ]
              }
            },
            sequence: ['block.recommended', 'block.cards'],
            preferences: {
              show_default_blocks: false
            }
          }
        },
        modal: {
          ondismiss: () => {
            setProcessingPayment(null);
            toast({
              title: "⚠️ PAYMENT CANCELLED",
              description: "Payment was cancelled. You can try again anytime.",
            });
          },
          escape: true,
          backdropclose: false
        }
      };

      try {
        if (!window.Razorpay) {
          throw new Error('Razorpay not available');
        }

        const razorpay = new window.Razorpay(options);

        // Handle payment failures with detailed error handling
        razorpay.on('payment.failed', function(response: any) {
          let errorMessage = "Payment failed. Please try again.";
          let errorTitle = "❌ PAYMENT FAILED";

          if (response.error) {
            const error = response.error;

            // Handle specific error codes
            if (error.code === 'BAD_REQUEST_ERROR') {
              if (error.description?.includes('international') ||
                  error.description?.includes('not supported') ||
                  error.description?.includes('International cards')) {
                errorTitle = "🌍 INTERNATIONAL CARD NOT SUPPORTED";
                errorMessage = `International cards are currently not enabled on our payment gateway.

Please try these alternatives:
• Use UPI (GPay, PhonePe, Paytm) - Recommended
• Use Net Banking from any Indian bank
• Use a domestic Indian card if available
• Try digital wallets (Paytm, Mobikwik)

Contact support: <EMAIL> if you need help.`;
              } else if (error.description?.includes('card')) {
                errorTitle = "💳 CARD ISSUE";
                errorMessage = "There's an issue with your card. Please check your card details or try a different payment method like UPI or Net Banking.";
              } else if (error.description?.includes('insufficient')) {
                errorTitle = "💰 INSUFFICIENT FUNDS";
                errorMessage = "Insufficient funds in your account. Please check your balance and try again.";
              } else {
                errorMessage = error.description || errorMessage;
              }
            } else if (error.code === 'GATEWAY_ERROR') {
              errorTitle = "🔌 GATEWAY ERROR";
              errorMessage = "Payment gateway is temporarily unavailable. Please try UPI or Net Banking, or try again in a few minutes.";
            } else if (error.code === 'NETWORK_ERROR') {
              errorTitle = "🌐 NETWORK ERROR";
              errorMessage = "Network connection issue. Please check your internet connection and try again.";
            } else if (error.description) {
              errorMessage = error.description;
            }
          }

          toast({
            variant: "destructive",
            title: errorTitle,
            description: errorMessage,
          });
          setProcessingPayment(null);
        });

        razorpay.open();
      } catch (razorpayError) {
        setProcessingPayment(null);
        toast({
          variant: "destructive",
          title: "❌ PAYMENT ERROR",
          description: "Could not initialize payment. Please try again.",
        });
      }
    } catch (error: any) {
      setProcessingPayment(null);

      let errorMessage = "Failed to initiate payment. Please try again.";

      if (error.message.includes('Razorpay')) {
        errorMessage = "Payment system unavailable. Please try again later.";
      } else if (error.message.includes('network') || error.message.includes('Network')) {
        errorMessage = "Network error. Please check your connection.";
      }

      toast({
        variant: "destructive",
        title: "❌ PAYMENT ERROR",
        description: errorMessage,
      });
    }
  };

  const handlePaymentSuccess = async (response: any, plan: PaymentPlan) => {
    try {
      // Verify payment signature for security
      if (response.razorpay_signature && response.razorpay_order_id) {
        const isValidSignature = PaymentSecurity.verifyPaymentSignature(
          response.razorpay_order_id,
          response.razorpay_payment_id,
          response.razorpay_signature
        );

        if (!isValidSignature) {
          throw new Error('Payment signature verification failed');
        }
      }

      const paymentData = {
        razorpayOrderId: response.razorpay_order_id || null,
        razorpayPaymentId: response.razorpay_payment_id,
        razorpaySignature: response.razorpay_signature || null,
        amount: plan.price,
        currency: 'INR',
        planType: plan.id,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        verified: response.razorpay_signature ? true : false
      };

      // Fraud detection
      const fraudIndicators = PaymentSecurity.detectFraudIndicators(paymentData);
      if (fraudIndicators.length > 0) {
        // Log fraud indicators but don't block payment (for now)
        console.warn('Fraud indicators detected:', fraudIndicators);
      }

      // Create payment record first
      await createPaymentRecord(currentUser!.uid, paymentData);

      // Update user subscription with retry logic
      let subscriptionUpdated = false;
      let retryCount = 0;
      const maxRetries = 3;

      while (!subscriptionUpdated && retryCount < maxRetries) {
        try {
          await updateUserSubscription(currentUser!.uid, plan.id as 'weekly' | 'monthly');
          subscriptionUpdated = true;
        } catch (subscriptionError) {
          retryCount++;

          if (retryCount < maxRetries) {
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
          } else {
            throw subscriptionError;
          }
        }
      }

      // Verify subscription was activated
      const subscriptionStatus = await checkUserSubscription(currentUser!.uid);

      if (subscriptionStatus.hasAccess) {
        toast({
          title: "🎉 PAYMENT SUCCESSFUL",
          description: `${plan.name} activated successfully! Welcome to Wolf CTF!`,
        });

        // Redirect to CTF page
        setTimeout(() => {
          navigate('/ctf');
        }, 2000);
      } else {
        throw new Error('Subscription verification failed');
      }

    } catch (error: any) {
      let errorMessage = "Payment received but failed to activate subscription. Please contact support.";
      let errorTitle = "❌ PAYMENT PROCESSING ERROR";

      if (error.message?.includes('permission-denied')) {
        errorTitle = "🔒 ACCESS DENIED";
        errorMessage = "Unable to activate subscription. Please contact support with your payment ID.";
      } else if (error.message?.includes('network')) {
        errorTitle = "🌐 NETWORK ERROR";
        errorMessage = "Network error during activation. Your payment was successful. Please refresh the page or contact support.";
      } else if (error.message?.includes('not-found')) {
        errorTitle = "👤 PROFILE ERROR";
        errorMessage = "User profile issue. Please contact support with your payment ID.";
      } else if (error.message?.includes('unavailable')) {
        errorTitle = "⏰ SERVICE UNAVAILABLE";
        errorMessage = "Service temporarily unavailable. Please try again in a few moments.";
      }

      // Include payment ID for support
      if (response.razorpay_payment_id) {
        errorMessage += ` Payment ID: ${response.razorpay_payment_id}`;
      }

      toast({
        variant: "destructive",
        title: errorTitle,
        description: errorMessage,
      });
    } finally {
      setProcessingPayment(null);
    }
  };



  if (loading || loadingProfile) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="terminal-cursor text-4xl mb-4">LOADING</div>
          <p className="text-muted-foreground font-mono">Initializing payment system...</p>
        </div>
      </div>
    );
  }

  if (!currentUser) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b-4 border-foreground bg-background sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tighter">WOLF CTF CHALLENGE</h1>
            <p className="text-sm text-muted-foreground font-mono">
              SECURE PAYMENT GATEWAY
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => navigate('/')}
            className="border-2"
          >
            ← BACK
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">

        {/* User Info */}
        <div className="mb-8 text-center">
          <h2 className="text-3xl font-bold mb-2">Choose Your Plan</h2>
          <p className="text-muted-foreground font-mono">
            Welcome {userProfile?.displayName || userProfile?.fullName || 'Hacker'}! 
            Select a subscription to access Wolf CTF challenges.
          </p>
        </div>

        {/* Subscription Status */}
        {subscriptionStatus && subscriptionStatus.status !== 'none' && (
          <div className="mb-8">
            <Card className={`p-4 border-2 ${
              subscriptionStatus.status === 'active' 
                ? 'border-terminal-green bg-terminal-green/10' 
                : 'border-yellow-500 bg-yellow-500/10'
            }`}>
              <div className="flex items-center gap-3">
                {subscriptionStatus.status === 'active' ? (
                  <CheckCircle className="h-5 w-5 text-terminal-green" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                )}
                <div>
                  <p className="font-bold">
                    Current Status: {subscriptionStatus.status.toUpperCase()}
                  </p>
                  {subscriptionStatus.expiryDate && (
                    <p className="text-sm text-muted-foreground">
                      {subscriptionStatus.status === 'active' ? 'Expires' : 'Expired'} on: {' '}
                      {subscriptionStatus.expiryDate.toLocaleDateString()}
                    </p>
                  )}
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Payment Plans */}
        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {paymentPlans.map((plan) => (
            <Card
              key={plan.id}
              className={`p-8 border-4 shadow-brutal-lg transition-all hover:scale-105 relative ${
                plan.popular 
                  ? 'border-accent bg-accent/5' 
                  : 'border-foreground hover:border-accent'
              }`}
            >
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-accent text-accent-foreground">
                  MOST POPULAR
                </Badge>
              )}
              
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                <div className="text-4xl font-bold mb-2">
                  ₹{plan.price}
                  <span className="text-lg text-muted-foreground">/{plan.duration}</span>
                </div>
                <div className="flex items-center justify-center gap-2 text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  <span>{plan.duration} access</span>
                </div>
              </div>

              <div className="space-y-3 mb-8">
                {plan.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-terminal-green flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </div>
                ))}
              </div>

              <Button
                onClick={() => handlePayment(plan)}
                disabled={processingPayment !== null}
                className={`w-full text-lg py-6 ${
                  plan.popular
                    ? 'bg-accent hover:bg-accent/90'
                    : ''
                }`}
              >
                {processingPayment === plan.id ? (
                  <div className="flex items-center gap-2">
                    <div className="terminal-cursor">PROCESSING</div>
                  </div>
                ) : (
                  `GET ${plan.name.toUpperCase()}`
                )}
              </Button>
            </Card>
          ))}
        </div>

        

        {/* Footer */}
        <div className="mt-12 text-center text-sm text-muted-foreground">
          <p className="mb-2">
            🔒 Payments are processed securely through Razorpay
          </p>
          <p>
            Need help? Contact <NAME_EMAIL>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Payment;

import { useEffect, useState } from 'react';
import { useAuth } from '@/components/AuthProvider';
import { useNavigate, useLocation } from 'react-router-dom';
import { checkUserSubscription } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';

interface UserFlowManagerProps {
  children: React.ReactNode;
}

export const UserFlowManager = ({ children }: UserFlowManagerProps) => {
  const { currentUser, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const [flowChecked, setFlowChecked] = useState(false);

  useEffect(() => {
    const manageUserFlow = async () => {
      // Skip flow management if still loading auth or already checked
      if (loading || flowChecked) return;

      // Skip flow management for certain paths
      const skipPaths = ['/payment', '/ctf', '/profile', '/admin'];
      if (skipPaths.includes(location.pathname)) {
        setFlowChecked(true);
        return;
      }

      // If user is not logged in and not on login page, stay on login
      if (!currentUser && location.pathname !== '/') {
        navigate('/', { replace: true });
        setFlowChecked(true);
        return;
      }

      // If user is logged in, check their subscription status
      if (currentUser) {
        try {
          console.log('UserFlowManager: Checking subscription for user:', currentUser.uid);
          const subscription = await checkUserSubscription(currentUser.uid);
          console.log('UserFlowManager: Subscription status:', subscription);

          if (subscription.hasAccess && subscription.status === 'active') {
            // User has active subscription
            if (location.pathname === '/') {
              // Redirect from login page to CTF
              toast({
                title: "🎯 WELCOME BACK",
                description: `Your ${subscription.subscriptionType} subscription is active!`,
              });
              navigate('/ctf', { replace: true });
            }
          } else {
            // User needs subscription
            if (location.pathname === '/') {
              // Redirect from login page to payment
              navigate('/payment', { replace: true });
            }
          }
        } catch (error) {
          console.error('UserFlowManager: Error checking subscription:', error);
          // On error, redirect to payment to be safe
          if (location.pathname === '/') {
            navigate('/payment', { replace: true });
          }
        }
      }

      setFlowChecked(true);
    };

    manageUserFlow();
  }, [currentUser, loading, location.pathname, navigate, toast, flowChecked]);

  // Reset flow check when user changes
  useEffect(() => {
    setFlowChecked(false);
  }, [currentUser]);

  return <>{children}</>;
};

export default UserFlowManager;

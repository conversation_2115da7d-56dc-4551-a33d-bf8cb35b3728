import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/components/AuthProvider';
import { checkUserSubscription, getUserProfile } from '@/lib/firebase';
import { 
  AlertTriangle, 
  CreditCard, 
  Globe, 
  RefreshCw, 
  CheckCircle, 
  XCircle,
  HelpCircle,
  Phone,
  Mail
} from 'lucide-react';

const PaymentTroubleshooting = () => {
  const { currentUser } = useAuth();
  const { toast } = useToast();
  const [checking, setChecking] = useState(false);
  const [subscriptionStatus, setSubscriptionStatus] = useState<any>(null);

  const checkSubscriptionStatus = async () => {
    if (!currentUser) return;
    
    setChecking(true);
    try {
      const status = await checkUserSubscription(currentUser.uid);
      setSubscriptionStatus(status);
      
      if (status.hasAccess) {
        toast({
          title: "✅ SUBSCRIPTION ACTIVE",
          description: `Your ${status.subscriptionType} subscription is active until ${status.expiryDate?.toLocaleDateString()}`,
        });
      } else {
        toast({
          variant: "destructive",
          title: "❌ NO ACTIVE SUBSCRIPTION",
          description: "You don't have an active subscription. Please purchase a plan to access CTF challenges.",
        });
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "❌ CHECK FAILED",
        description: "Failed to check subscription status. Please try again.",
      });
    } finally {
      setChecking(false);
    }
  };

  const commonIssues = [
    {
      icon: <Globe className="h-5 w-5" />,
      title: "International Cards Not Supported",
      description: "International cards are currently not enabled on our payment gateway",
      solutions: [
        "Use UPI (GPay, PhonePe, Paytm) - Most reliable option",
        "Try Net Banking from any Indian bank",
        "Use digital wallets (Paytm, Mobikwik, FreeCharge)",
        "Use a domestic Indian card if you have one",
        "Contact support for assistance: <EMAIL>"
      ]
    },
    {
      icon: <CreditCard className="h-5 w-5" />,
      title: "Card Declined",
      description: "Your card was declined during payment",
      solutions: [
        "Check if you have sufficient balance",
        "Verify card details (number, expiry, CVV)",
        "Try a different card",
        "Contact your bank to check for blocks"
      ]
    },
    {
      icon: <AlertTriangle className="h-5 w-5" />,
      title: "Payment Successful but No Access",
      description: "Payment went through but subscription not activated",
      solutions: [
        "Wait 2-3 minutes and refresh the page",
        "Check your subscription status below",
        "Contact support with your payment ID",
        "Try logging out and logging back in"
      ]
    },
    {
      icon: <XCircle className="h-5 w-5" />,
      title: "Payment Gateway Error",
      description: "Technical issues with payment processing",
      solutions: [
        "Try again after a few minutes",
        "Clear browser cache and cookies",
        "Try a different browser or device",
        "Check your internet connection"
      ]
    }
  ];

  const paymentMethods = [
    {
      name: "Credit/Debit Cards",
      supported: "Visa, Mastercard, RuPay",
      note: "International cards may have restrictions"
    },
    {
      name: "UPI",
      supported: "All UPI apps (GPay, PhonePe, Paytm, etc.)",
      note: "Recommended for Indian users"
    },
    {
      name: "Net Banking",
      supported: "All major Indian banks",
      note: "Secure and reliable"
    },
    {
      name: "Wallets",
      supported: "Paytm, Mobikwik, etc.",
      note: "Quick and convenient"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Subscription Status Check */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold">Check Subscription Status</h3>
          <Button 
            onClick={checkSubscriptionStatus} 
            disabled={checking || !currentUser}
            size="sm"
          >
            {checking ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Checking...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Check Status
              </>
            )}
          </Button>
        </div>
        
        {subscriptionStatus && (
          <div className="p-4 rounded-lg bg-muted">
            <div className="flex items-center gap-2 mb-2">
              {subscriptionStatus.hasAccess ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              <span className="font-medium">
                {subscriptionStatus.hasAccess ? 'Active Subscription' : 'No Active Subscription'}
              </span>
            </div>
            {subscriptionStatus.hasAccess && (
              <div className="text-sm text-muted-foreground">
                <p>Plan: {subscriptionStatus.subscriptionType}</p>
                <p>Expires: {subscriptionStatus.expiryDate?.toLocaleDateString()}</p>
              </div>
            )}
          </div>
        )}
      </Card>

      {/* Common Issues */}
      <Card className="p-6">
        <h3 className="text-lg font-bold mb-4">Common Payment Issues</h3>
        <div className="space-y-4">
          {commonIssues.map((issue, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="flex items-center gap-3 mb-3">
                {issue.icon}
                <div>
                  <h4 className="font-medium">{issue.title}</h4>
                  <p className="text-sm text-muted-foreground">{issue.description}</p>
                </div>
              </div>
              <div className="ml-8">
                <p className="text-sm font-medium mb-2">Solutions:</p>
                <ul className="text-sm space-y-1">
                  {issue.solutions.map((solution, idx) => (
                    <li key={idx} className="flex items-start gap-2">
                      <span className="text-muted-foreground">•</span>
                      <span>{solution}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Supported Payment Methods */}
      <Card className="p-6">
        <h3 className="text-lg font-bold mb-4">Supported Payment Methods</h3>
        <div className="grid md:grid-cols-2 gap-4">
          {paymentMethods.map((method, index) => (
            <div key={index} className="border rounded-lg p-4">
              <h4 className="font-medium mb-2">{method.name}</h4>
              <p className="text-sm text-muted-foreground mb-2">{method.supported}</p>
              <Badge variant="outline" className="text-xs">
                {method.note}
              </Badge>
            </div>
          ))}
        </div>
      </Card>

      {/* Contact Support */}
      <Card className="p-6">
        <h3 className="text-lg font-bold mb-4">Need Help?</h3>
        <div className="space-y-4">
          <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
            <Mail className="h-5 w-5 text-blue-500" />
            <div>
              <p className="font-medium">Email Support</p>
              <p className="text-sm text-muted-foreground"><EMAIL></p>
            </div>
          </div>
          
          <div className="p-4 border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-950">
            <div className="flex items-start gap-2">
              <HelpCircle className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <p className="font-medium text-blue-900 dark:text-blue-100">
                  When contacting support, please include:
                </p>
                <ul className="text-sm text-blue-800 dark:text-blue-200 mt-2 space-y-1">
                  <li>• Your email address used for registration</li>
                  <li>• Payment ID (if payment was successful)</li>
                  <li>• Screenshot of the error message</li>
                  <li>• Payment method used (card/UPI/netbanking)</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PaymentTroubleshooting;

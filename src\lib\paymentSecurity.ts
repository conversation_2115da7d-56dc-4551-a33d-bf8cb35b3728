import CryptoJS from 'crypto-js';

// Enhanced security utilities for payment processing
export class PaymentSecurity {
  private static readonly RAZORPAY_KEY_SECRET = 'C05wPHxAUxNQKm4PDaWXim9o';

  /**
   * Verify Razorpay payment signature
   * This should ideally be done on the server-side for maximum security
   */
  static verifyPaymentSignature(
    orderId: string,
    paymentId: string,
    signature: string
  ): boolean {
    try {
      const body = orderId + '|' + paymentId;
      const expectedSignature = CryptoJS.HmacSHA256(body, this.RAZORPAY_KEY_SECRET).toString();
      
      return expectedSignature === signature;
    } catch (error) {
      console.error('Signature verification error:', error);
      return false;
    }
  }

  /**
   * Generate secure order ID with timestamp and user info
   */
  static generateOrderId(userId: string): string {
    const timestamp = Date.now();
    const userHash = CryptoJS.SHA256(userId).toString().slice(0, 8);
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    
    return `order_${timestamp}_${userHash}_${randomSuffix}`;
  }

  /**
   * Validate payment amount to prevent tampering
   */
  static validatePaymentAmount(receivedAmount: number, expectedAmount: number): boolean {
    return receivedAmount === expectedAmount * 100; // Razorpay uses paise
  }

  /**
   * Generate secure payment notes with user context
   */
  static generatePaymentNotes(userId: string, planType: string): Record<string, string> {
    return {
      userId: userId,
      planType: planType,
      timestamp: new Date().toISOString(),
      source: 'wolf_ctf_web',
      version: '1.0'
    };
  }

  /**
   * Sanitize user input for payment processing
   */
  static sanitizeUserInput(input: string): string {
    return input.trim().replace(/[<>\"']/g, '');
  }

  /**
   * Generate payment session token for additional security
   */
  static generateSessionToken(userId: string, planType: string): string {
    const data = `${userId}:${planType}:${Date.now()}`;
    return CryptoJS.SHA256(data).toString();
  }

  /**
   * Validate payment session token
   */
  static validateSessionToken(
    token: string,
    userId: string,
    planType: string,
    maxAgeMinutes: number = 30
  ): boolean {
    try {
      // In a real implementation, you'd store and validate tokens server-side
      // This is a simplified client-side validation
      const tokenAge = Date.now() - parseInt(token.slice(-13)); // Extract timestamp
      const maxAge = maxAgeMinutes * 60 * 1000;
      
      return tokenAge <= maxAge;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  /**
   * Log security events for monitoring
   */
  static logSecurityEvent(
    eventType: 'payment_initiated' | 'payment_success' | 'payment_failed' | 'signature_verification',
    userId: string,
    details: Record<string, any>
  ): void {
    const securityLog = {
      timestamp: new Date().toISOString(),
      eventType,
      userId,
      details,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // In production, send this to your security monitoring service
    console.log('Security Event:', securityLog);
    
    // Store in localStorage for debugging (remove in production)
    const existingLogs = JSON.parse(localStorage.getItem('security_logs') || '[]');
    existingLogs.push(securityLog);
    
    // Keep only last 100 logs
    if (existingLogs.length > 100) {
      existingLogs.splice(0, existingLogs.length - 100);
    }
    
    localStorage.setItem('security_logs', JSON.stringify(existingLogs));
  }

  /**
   * Detect potential fraud indicators
   */
  static detectFraudIndicators(paymentData: any): string[] {
    const indicators: string[] = [];

    // Check for rapid successive payments
    const recentPayments = JSON.parse(localStorage.getItem('recent_payments') || '[]');
    const now = Date.now();
    const recentCount = recentPayments.filter((p: any) => now - p.timestamp < 60000).length;
    
    if (recentCount > 3) {
      indicators.push('rapid_payments');
    }

    // Check for unusual amounts
    if (paymentData.amount && (paymentData.amount < 9 || paymentData.amount > 29)) {
      indicators.push('unusual_amount');
    }

    // Check for browser inconsistencies
    if (!navigator.userAgent || navigator.userAgent.length < 50) {
      indicators.push('suspicious_browser');
    }

    return indicators;
  }

  /**
   * Rate limiting for payment attempts
   */
  static checkRateLimit(userId: string, maxAttempts: number = 5, windowMinutes: number = 15): boolean {
    const key = `payment_attempts_${userId}`;
    const attempts = JSON.parse(localStorage.getItem(key) || '[]');
    const now = Date.now();
    const windowMs = windowMinutes * 60 * 1000;

    // Filter recent attempts
    const recentAttempts = attempts.filter((timestamp: number) => now - timestamp < windowMs);

    if (recentAttempts.length >= maxAttempts) {
      return false; // Rate limit exceeded
    }

    // Add current attempt
    recentAttempts.push(now);
    localStorage.setItem(key, JSON.stringify(recentAttempts));

    return true; // Within rate limit
  }

  /**
   * Encrypt sensitive data for local storage
   */
  static encryptData(data: string, key: string): string {
    return CryptoJS.AES.encrypt(data, key).toString();
  }

  /**
   * Decrypt sensitive data from local storage
   */
  static decryptData(encryptedData: string, key: string): string {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, key);
      return bytes.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error('Decryption error:', error);
      return '';
    }
  }
}

export default PaymentSecurity;

import { useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'react-router-dom';

interface SecurityProviderProps {
  children: React.ReactNode;
}

export const SecurityProvider = ({ children }: SecurityProviderProps) => {
  const { toast } = useToast();
  const location = useLocation();

  // Disable auto-blur for payment page
  const isPaymentPage = location.pathname === '/payment';

  useEffect(() => {
    // Security: Block right-click context menu
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      toast({
        variant: "destructive",
        title: "🚫 ACCESS DENIED",
        description: "Right-click is disabled for security",
      });
      return false;
    };

    // Security: Block common developer shortcuts and keys
    const handleKeyDown = (e: KeyboardEvent) => {
      // Block F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U, Ctrl+Shift+C
      if (
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'J' || e.key === 'C')) ||
        (e.ctrlKey && e.key === 'U') ||
        (e.ctrlKey && e.key === 'S') || // Block Ctrl+S (save page)
        e.key === 'F5' || // Block F5 refresh in some contexts
        (e.ctrlKey && e.key === 'R') // Block Ctrl+R refresh
      ) {
        e.preventDefault();
        toast({
          variant: "destructive",
          title: "🚫 BLOCKED",
          description: "Developer tools access is restricted",
        });
        return false;
      }
    };

    // Security: Detect developer tools opening
    const detectDevTools = () => {
      const threshold = 160;
      const widthThreshold = window.outerWidth - window.innerWidth > threshold;
      const heightThreshold = window.outerHeight - window.innerHeight > threshold;
      
      if (widthThreshold || heightThreshold) {
        toast({
          variant: "destructive",
          title: "⚠️ SECURITY ALERT",
          description: "Developer tools detected. Please close them.",
        });
        // Optionally redirect or take other action
        // window.location.href = '/';
      }
    };

    // Security: Block text selection and drag
    const handleSelectStart = (e: Event) => {
      e.preventDefault();
      return false;
    };

    const handleDragStart = (e: DragEvent) => {
      e.preventDefault();
      return false;
    };

    // Security: Block print screen
    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === 'PrintScreen') {
        toast({
          variant: "destructive",
          title: "🚫 SCREENSHOT BLOCKED",
          description: "Screenshots are not allowed",
        });
      }
    };

    // Security: Blur content when window loses focus (anti-screenshot)
    // Disabled for payment page to prevent payment interruptions
    const handleVisibilityChange = () => {
      if (isPaymentPage) return; // Skip blur for payment page

      if (document.hidden) {
        document.body.style.filter = 'blur(5px)';
      } else {
        document.body.style.filter = 'none';
      }
    };

    const handleBlur = () => {
      if (isPaymentPage) return; // Skip blur for payment page
      document.body.style.filter = 'blur(5px)';
    };

    const handleFocus = () => {
      if (isPaymentPage) return; // Skip blur for payment page
      document.body.style.filter = 'none';
    };

    // Add event listeners
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    document.addEventListener('selectstart', handleSelectStart);
    document.addEventListener('dragstart', handleDragStart);

    // Only add blur event listeners if not on payment page
    if (!isPaymentPage) {
      document.addEventListener('visibilitychange', handleVisibilityChange);
      window.addEventListener('blur', handleBlur);
      window.addEventListener('focus', handleFocus);
    }

    // Check for dev tools periodically
    const devToolsInterval = setInterval(detectDevTools, 1000);

    // Security: Disable console
    const originalConsole = { ...console };
    console.log = () => {};
    console.warn = () => {};
    console.error = () => {};
    console.info = () => {};
    console.debug = () => {};

    // Security: Override common debugging functions
    (window as any).eval = () => {
      toast({
        variant: "destructive",
        title: "🚫 EVAL BLOCKED",
        description: "Code execution is not allowed",
      });
    };

    // Cleanup function
    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
      document.removeEventListener('selectstart', handleSelectStart);
      document.removeEventListener('dragstart', handleDragStart);

      // Only remove blur event listeners if they were added
      if (!isPaymentPage) {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('blur', handleBlur);
        window.removeEventListener('focus', handleFocus);
      }

      clearInterval(devToolsInterval);

      // Restore console (for development)
      if (process.env.NODE_ENV === 'development') {
        Object.assign(console, originalConsole);
      }

      // Remove blur filter
      document.body.style.filter = 'none';
    };
  }, [toast, isPaymentPage]);

  return <>{children}</>;
};

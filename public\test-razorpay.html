<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Razorpay Test</title>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #000;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #333;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Razorpay LIVE Payment Test</h1>
        <p><strong>⚠️ WARNING: This uses LIVE credentials - Real money will be charged!</strong></p>

        <h3>Live Credentials:</h3>
        <p><strong>Key ID:</strong> rzp_live_ds31O8lmHsfWi4</p>
        
        <h3>⚠️ LIVE Payment Options (REAL MONEY):</h3>
        <button onclick="testWeeklyPayment()">LIVE Weekly Payment (₹9) - REAL CHARGE</button>
        <button onclick="testMonthlyPayment()">LIVE Monthly Payment (₹29) - REAL CHARGE</button>

        <div id="result" class="result"></div>

        <h3>⚠️ Use REAL Payment Methods:</h3>
        <ul>
            <li><strong>Real Card:</strong> Use your actual debit/credit card</li>
            <li><strong>UPI:</strong> Use your real UPI ID</li>
            <li><strong>Net Banking:</strong> Use your actual bank account</li>
            <li><strong>⚠️ WARNING:</strong> Test cards will NOT work in live mode</li>
        </ul>
        
        <h3>⚠️ LIVE Testing Instructions:</h3>
        <ol>
            <li><strong>WARNING:</strong> This will charge real money!</li>
            <li>Only proceed if you understand this is a live transaction</li>
            <li>Click one of the payment buttons above</li>
            <li>Razorpay payment modal should open</li>
            <li>Enter your REAL payment details (card/UPI/netbanking)</li>
            <li>Complete the payment - YOU WILL BE CHARGED</li>
            <li>Check the result below</li>
        </ol>
    </div>

    <script>
        const RAZORPAY_KEY_ID = 'rzp_live_ds31O8lmHsfWi4';
        
        function showResult(message, isSuccess) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.style.display = 'block';
        }
        
        function testWeeklyPayment() {
            testPayment(900, 'Weekly Access - 7 days');
        }
        
        function testMonthlyPayment() {
            testPayment(2900, 'Monthly Access - 30 days');
        }
        
        function testPayment(amount, description) {
            if (!window.Razorpay) {
                showResult('❌ Razorpay script not loaded. Please refresh the page.', false);
                return;
            }
            
            const options = {
                key: RAZORPAY_KEY_ID,
                amount: amount, // Amount in paise
                currency: 'INR',
                name: 'Wolf CTF Challenge',
                description: description,
                handler: function(response) {
                    showResult(
                        '✅ Payment Successful!\n' +
                        'Payment ID: ' + response.razorpay_payment_id + '\n' +
                        'Order ID: ' + (response.razorpay_order_id || 'N/A') + '\n' +
                        'Signature: ' + (response.razorpay_signature || 'N/A'),
                        true
                    );
                    console.log('Payment Success:', response);
                },
                prefill: {
                    name: 'Test User',
                    email: '<EMAIL>',
                    contact: '9999999999'
                },
                notes: {
                    test: 'true',
                    timestamp: new Date().toISOString()
                },
                theme: {
                    color: '#000000'
                },
                modal: {
                    ondismiss: function() {
                        showResult('⚠️ Payment was cancelled by user', false);
                    }
                }
            };
            
            try {
                const rzp = new Razorpay(options);
                
                rzp.on('payment.failed', function(response) {
                    showResult(
                        '❌ Payment Failed!\n' +
                        'Error: ' + response.error.description + '\n' +
                        'Code: ' + response.error.code,
                        false
                    );
                    console.error('Payment Failed:', response.error);
                });
                
                rzp.open();
            } catch (error) {
                showResult('❌ Error initializing payment: ' + error.message, false);
                console.error('Razorpay Error:', error);
            }
        }
        
        // Check if Razorpay is loaded
        window.addEventListener('load', function() {
            if (window.Razorpay) {
                console.log('✅ Razorpay loaded successfully');
            } else {
                console.error('❌ Razorpay failed to load');
                showResult('❌ Razorpay script failed to load. Please check your internet connection.', false);
            }
        });
    </script>
</body>
</html>
